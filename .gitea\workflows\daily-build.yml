name: Daily Build and Code Analysis

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  schedule:
    # Run daily at 2:00 AM UTC (adjust timezone as needed)
    - cron: '0 2 * * *'
  workflow_dispatch:
    # Allow manual trigger

jobs:
  build-and-analyze:
    runs-on: windows-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup environment
      run: |
        echo Setting up build environment...
        echo Current directory: %CD%
        echo Repository contents:
        dir

    - name: Check TI CCS installation
      run: |
        if exist "C:\ti\ccs2020\ccs\eclipse\ccs-server-cli.bat" (
          echo TI CCS found at expected location
        ) else (
          echo Warning: TI CCS not found at C:\ti\ccs2020\ccs\
          echo Available TI installations:
          if exist "C:\ti" dir C:\ti
        )
      shell: cmd

    - name: Check Cppcheck installation
      run: |
        if exist "C:\Program Files\Cppcheck" (
          echo Cppcheck found
        ) else (
          echo Warning: Cppcheck not found at C:\Program Files\Cppcheck
        )
      shell: cmd
      
    - name: Run IDE Clean Build
      run: |
        echo Running IDE Clean Build...
        cd dailyBuildConfigs
        if exist "ideCleanBuild.bat" (
          call ideCleanBuild.bat
        ) else (
          echo Error: ideCleanBuild.bat not found
          exit /b 1
        )
      shell: cmd
      continue-on-error: true

    - name: Run IDE Build
      run: |
        echo Running IDE Build...
        cd dailyBuildConfigs
        if exist "idebuild.bat" (
          call idebuild.bat
        ) else (
          echo Error: idebuild.bat not found
          exit /b 1
        )
      shell: cmd
      continue-on-error: true

    - name: Run Cppcheck Analysis
      run: |
        echo Running Cppcheck Analysis...
        cd dailyBuildConfigs
        if exist "cppchk.bat" (
          call cppchk.bat
        ) else (
          echo Error: cppchk.bat not found
          exit /b 1
        )
      shell: cmd
      continue-on-error: true
      
    - name: Upload Cppcheck Results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: cppcheck-results
        path: |
          cppcheck-result.txt
        retention-days: 30
        
    - name: Upload Build Artifacts
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: build-artifacts
        path: |
          Debug/
          *.out
          *.elf
        retention-days: 7
        
    - name: Summary Report
      if: always()
      run: |
        echo === Build Summary ===
        echo Repository: ${{ github.repository }}
        echo Branch: ${{ github.ref_name }}
        echo Commit: ${{ github.sha }}
        echo Triggered by: ${{ github.event_name }}
        echo.

        if exist "cppcheck-result.txt" (
          echo === Cppcheck Results ===
          type cppcheck-result.txt
          echo ... ^(see full report in artifacts^)
        ) else (
          echo No cppcheck results found
        )
      shell: cmd
