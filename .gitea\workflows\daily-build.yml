name: Daily Build and Code Analysis

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  schedule:
    # Run daily at 2:00 AM UTC (adjust timezone as needed)
    - cron: '0 2 * * *'
  workflow_dispatch:
    # Allow manual trigger

jobs:
  build-and-analyze:
    runs-on: windows-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup environment
      run: |
        echo "Setting up build environment..."
        echo "Current directory: $(pwd)"
        echo "Repository contents:"
        dir
        
    - name: Check TI CCS installation
      run: |
        if (Test-Path "C:\ti\ccs2020\ccs\eclipse\ccs-server-cli.bat") {
          echo "TI CCS found at expected location"
        } else {
          echo "Warning: TI CCS not found at C:\ti\ccs2020\ccs\"
          echo "Available TI installations:"
          if (Test-Path "C:\ti") { dir C:\ti }
        }
      shell: powershell
      
    - name: Check Cppcheck installation
      run: |
        if (Test-Path "C:\Program Files\Cppcheck") {
          echo "Cppcheck found"
        } else {
          echo "Warning: Cppcheck not found at C:\Program Files\Cppcheck"
        }
      shell: powershell
      
    - name: Run IDE Clean Build
      run: |
        echo "Running IDE Clean Build..."
        cd dailyBuildConfigs
        if (Test-Path "ideCleanBuild.bat") {
          .\ideCleanBuild.bat
        } else {
          echo "Error: ideCleanBuild.bat not found"
          exit 1
        }
      shell: powershell
      continue-on-error: true
      
    - name: Run IDE Build
      run: |
        echo "Running IDE Build..."
        cd dailyBuildConfigs
        if (Test-Path "idebuild.bat") {
          .\idebuild.bat
        } else {
          echo "Error: idebuild.bat not found"
          exit 1
        }
      shell: powershell
      continue-on-error: true
      
    - name: Run Cppcheck Analysis
      run: |
        echo "Running Cppcheck Analysis..."
        cd dailyBuildConfigs
        if (Test-Path "cppchk.bat") {
          .\cppchk.bat
        } else {
          echo "Error: cppchk.bat not found"
          exit 1
        }
      shell: powershell
      continue-on-error: true
      
    - name: Upload Cppcheck Results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: cppcheck-results
        path: |
          cppcheck-result.txt
        retention-days: 30
        
    - name: Upload Build Artifacts
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: build-artifacts
        path: |
          Debug/
          *.out
          *.elf
        retention-days: 7
        
    - name: Summary Report
      if: always()
      run: |
        echo "=== Build Summary ==="
        echo "Repository: ${{ github.repository }}"
        echo "Branch: ${{ github.ref_name }}"
        echo "Commit: ${{ github.sha }}"
        echo "Triggered by: ${{ github.event_name }}"
        echo ""
        
        if (Test-Path "cppcheck-result.txt") {
          echo "=== Cppcheck Results ==="
          Get-Content "cppcheck-result.txt" | Select-Object -First 20
          if ((Get-Content "cppcheck-result.txt").Count -gt 20) {
            echo "... (truncated, see full report in artifacts)"
          }
        } else {
          echo "No cppcheck results found"
        }
      shell: powershell
