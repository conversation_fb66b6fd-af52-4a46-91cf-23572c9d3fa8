@echo off
echo Testing Gitea Actions configuration...
echo.

echo Checking .gitea directory structure...
if exist ".gitea\workflows" (
    echo [OK] .gitea\workflows directory exists
) else (
    echo [ERROR] .gitea\workflows directory not found
    exit /b 1
)

echo.
echo Checking workflow files...

if exist ".gitea\workflows\daily-build.yml" (
    echo [OK] daily-build.yml found
) else (
    echo [ERROR] daily-build.yml not found
)

if exist ".gitea\workflows\execute-daily-build-scripts.yml" (
    echo [OK] execute-daily-build-scripts.yml found
) else (
    echo [ERROR] execute-daily-build-scripts.yml not found
)

if exist ".gitea\workflows\execute-all-scripts.yml" (
    echo [OK] execute-all-scripts.yml found
) else (
    echo [ERROR] execute-all-scripts.yml not found
)

if exist ".gitea\workflows\README.md" (
    echo [OK] README.md found
) else (
    echo [ERROR] README.md not found
)

echo.
echo Checking dailyBuildConfigs directory...
if exist "dailyBuildConfigs" (
    echo [OK] dailyBuildConfigs directory exists
    echo.
    echo Available batch files:
    dir dailyBuildConfigs\*.bat /b 2>nul
    if errorlevel 1 (
        echo [WARNING] No .bat files found in dailyBuildConfigs
    )
) else (
    echo [ERROR] dailyBuildConfigs directory not found
)

echo.
echo Testing batch file execution (dry run)...
cd dailyBuildConfigs 2>nul
if errorlevel 1 (
    echo [ERROR] Cannot access dailyBuildConfigs directory
    exit /b 1
)

for %%f in (*.bat) do (
    echo [INFO] Found batch file: %%f
    if exist "%%f" (
        echo [OK] %%f is accessible
    ) else (
        echo [ERROR] %%f is not accessible
    )
)

cd ..

echo.
echo ========================================
echo Configuration test completed!
echo ========================================
echo.
echo Next steps:
echo 1. Commit these files to your Git repository
echo 2. Push to your Gitea server
echo 3. Check the Actions tab in Gitea web interface
echo 4. Manually trigger a workflow to test execution
echo.
echo Available workflows:
echo - daily-build.yml: Complete build with scheduling
echo - execute-daily-build-scripts.yml: Step-by-step execution
echo - execute-all-scripts.yml: Simple all-in-one execution
echo.
