# Gitea act_runner Status Checker
# Run this script to check if act_runner is properly running

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Checking Gitea act_runner Status" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 1. Check running processes
Write-Host "1. Checking running processes..." -ForegroundColor Yellow
$actRunnerProcesses = Get-Process | Where-Object { $_.ProcessName -like "*act*runner*" -or $_.ProcessName -like "*gitea*" }

if ($actRunnerProcesses) {
    Write-Host "[OK] Found act_runner related processes:" -ForegroundColor Green
    $actRunnerProcesses | Format-Table ProcessName, Id, CPU, WorkingSet -AutoSize
} else {
    Write-Host "[WARNING] No act_runner processes found" -ForegroundColor Red
    
    # Check for any processes with 'act' or 'runner' in name
    $relatedProcesses = Get-Process | Where-Object { $_.ProcessName -like "*act*" -or $_.ProcessName -like "*runner*" }
    if ($relatedProcesses) {
        Write-Host "[INFO] Found related processes:" -ForegroundColor Yellow
        $relatedProcesses | Format-Table ProcessName, Id -AutoSize
    }
}

Write-Host ""

# 2. Check Windows Services
Write-Host "2. Checking Windows Services..." -ForegroundColor Yellow
$actServices = Get-Service | Where-Object { $_.Name -like "*act*" -or $_.DisplayName -like "*act*" -or $_.DisplayName -like "*gitea*" }

if ($actServices) {
    Write-Host "[OK] Found act_runner related services:" -ForegroundColor Green
    $actServices | Format-Table Name, Status, DisplayName -AutoSize
} else {
    Write-Host "[INFO] No act_runner services found" -ForegroundColor Yellow
}

Write-Host ""

# 3. Check common installation paths
Write-Host "3. Checking common installation paths..." -ForegroundColor Yellow
$commonPaths = @(
    "C:\act_runner",
    "C:\gitea",
    "C:\Program Files\act_runner",
    "C:\Program Files\Gitea",
    "C:\tools\act_runner",
    "$env:USERPROFILE\act_runner",
    "$env:USERPROFILE\Downloads\act_runner"
)

foreach ($path in $commonPaths) {
    if (Test-Path $path) {
        Write-Host "[FOUND] Directory: $path" -ForegroundColor Green
        
        $exeFiles = Get-ChildItem -Path $path -Filter "*.exe" -Recurse -ErrorAction SilentlyContinue | Where-Object { $_.Name -like "*act*" -or $_.Name -like "*gitea*" }
        if ($exeFiles) {
            Write-Host "  Executables found:" -ForegroundColor Cyan
            $exeFiles | ForEach-Object { Write-Host "    $($_.FullName)" -ForegroundColor White }
        }
        
        $configFiles = Get-ChildItem -Path $path -Filter "*.yaml" -Recurse -ErrorAction SilentlyContinue
        if ($configFiles) {
            Write-Host "  Config files found:" -ForegroundColor Cyan
            $configFiles | ForEach-Object { Write-Host "    $($_.FullName)" -ForegroundColor White }
        }
    }
}

Write-Host ""

# 4. Check network connections
Write-Host "4. Checking network connections..." -ForegroundColor Yellow
$connections = Get-NetTCPConnection -ErrorAction SilentlyContinue | Where-Object { $_.LocalPort -eq 3000 -or $_.LocalPort -eq 8080 -or $_.RemotePort -eq 3000 -or $_.RemotePort -eq 8080 }

if ($connections) {
    Write-Host "[INFO] Found relevant network connections:" -ForegroundColor Green
    $connections | Format-Table LocalAddress, LocalPort, RemoteAddress, RemotePort, State -AutoSize
} else {
    Write-Host "[INFO] No connections on common Gitea ports (3000, 8080) found" -ForegroundColor Yellow
}

Write-Host ""

# 5. Check Event Logs
Write-Host "5. Checking recent Event Logs..." -ForegroundColor Yellow
try {
    $recentEvents = Get-WinEvent -FilterHashtable @{LogName='Application'; StartTime=(Get-Date).AddHours(-24)} -MaxEvents 50 -ErrorAction SilentlyContinue | 
                   Where-Object { $_.Message -like "*act*" -or $_.Message -like "*gitea*" -or $_.Message -like "*runner*" }
    
    if ($recentEvents) {
        Write-Host "[INFO] Found recent relevant events:" -ForegroundColor Green
        $recentEvents | Select-Object TimeCreated, LevelDisplayName, Message | Format-Table -Wrap
    } else {
        Write-Host "[INFO] No recent relevant events found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "[WARNING] Could not access Event Logs: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 6. Test if act_runner executable works
Write-Host "6. Testing act_runner executable..." -ForegroundColor Yellow
$actRunnerPaths = @()

# Find all potential act_runner executables
foreach ($path in $commonPaths) {
    if (Test-Path $path) {
        $exes = Get-ChildItem -Path $path -Filter "*act*runner*.exe" -Recurse -ErrorAction SilentlyContinue
        $actRunnerPaths += $exes.FullName
    }
}

if ($actRunnerPaths) {
    foreach ($exePath in $actRunnerPaths) {
        Write-Host "[TESTING] $exePath" -ForegroundColor Cyan
        try {
            $result = & $exePath --version 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Host "[OK] Version: $result" -ForegroundColor Green
            } else {
                Write-Host "[WARNING] Exit code: $LASTEXITCODE, Output: $result" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "[ERROR] Failed to execute: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
} else {
    Write-Host "[WARNING] No act_runner executables found" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Manual Verification Steps:" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "A. Check Gitea Web Interface:" -ForegroundColor Yellow
Write-Host "   1. Go to your Gitea instance (e.g., http://localhost:3000)" -ForegroundColor White
Write-Host "   2. Login as admin" -ForegroundColor White
Write-Host "   3. Go to Site Administration > Actions > Runners" -ForegroundColor White
Write-Host "   4. Verify runners are listed and show 'Online' status" -ForegroundColor White
Write-Host ""

Write-Host "B. Check Repository Actions:" -ForegroundColor Yellow
Write-Host "   1. Go to your repository" -ForegroundColor White
Write-Host "   2. Click 'Actions' tab" -ForegroundColor White
Write-Host "   3. Look for workflow runs and their status" -ForegroundColor White
Write-Host "   4. Try manually triggering a workflow" -ForegroundColor White
Write-Host ""

Write-Host "C. Check Runner Configuration:" -ForegroundColor Yellow
Write-Host "   1. Find your runner's config.yaml file" -ForegroundColor White
Write-Host "   2. Verify the instance URL and token are correct" -ForegroundColor White
Write-Host "   3. Check runner labels match your workflow requirements" -ForegroundColor White
Write-Host ""

Write-Host "D. Common Commands:" -ForegroundColor Yellow
Write-Host "   Register runner: act_runner.exe register --instance http://your-gitea --token YOUR_TOKEN" -ForegroundColor White
Write-Host "   Start daemon: act_runner.exe daemon --config config.yaml" -ForegroundColor White
Write-Host "   Check version: act_runner.exe --version" -ForegroundColor White
Write-Host ""

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Check completed!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
