# Gitea Actions 工作流程說明

本目錄包含用於自動執行 dailyBuildConfigs 目錄中批次檔的 Gitea Actions 工作流程。

## 工作流程文件

### 1. `daily-build.yml`
完整的每日構建和代碼分析工作流程，使用 DOS command line 執行：
- IDE 清理構建
- IDE 構建
- Cppcheck 代碼分析
- 構建產物上傳
- 詳細的執行報告

**觸發條件：**
- 推送到 main/master/develop 分支
- Pull Request 到 main/master/develop 分支
- 每日定時執行（UTC 時間 2:00 AM）
- 手動觸發

### 2. `execute-daily-build-scripts.yml`
使用 DOS command line 分別執行每個批次檔：
- 依序執行 cppchk.bat、ideCleanBuild.bat、idebuild.bat
- 提供詳細的執行狀態報告
- 上傳執行日誌和結果文件

**觸發條件：**
- 推送到 main/master/develop 分支
- Pull Request 到 main/master/develop 分支
- 手動觸發

### 3. `execute-all-scripts.yml`
最簡潔的版本，動態創建執行腳本：
- 動態生成一個批次檔來執行所有腳本
- 使用純 DOS 批次檔語法
- 提供錯誤計數和摘要報告

**觸發條件：**
- 推送到 main/master/develop 分支
- Pull Request 到 main/master/develop 分支
- 手動觸發

## 當前 dailyBuildConfigs 目錄中的批次檔

1. **cppchk.bat** - 執行 Cppcheck 代碼靜態分析
2. **ideCleanBuild.bat** - 執行 TI CCS IDE 清理構建
3. **idebuild.bat** - 執行 TI CCS IDE 構建

## 主要特點

1. **DOS Command Line**：所有工作流程都使用 DOS 批次檔語法執行，確保與 Windows 環境完全兼容
2. **錯誤處理**：即使某個批次檔失敗，其他的仍會繼續執行
3. **詳細報告**：提供每個批次檔的執行狀態和退出代碼
4. **產物保存**：自動上傳構建結果和分析報告
5. **環境檢查**：驗證必要的工具（TI CCS、Cppcheck）是否安裝
6. **多種執行方式**：提供三種不同的工作流程以適應不同需求

## 使用方法

### 自動觸發
工作流程會在以下情況自動執行：
- 當您推送代碼到主要分支時
- 當您創建或更新 Pull Request 時
- 每日定時執行（僅 daily-build.yml）

### 手動觸發
1. 進入 Gitea 倉庫頁面
2. 點擊 "Actions" 標籤
3. 選擇要執行的工作流程
4. 點擊 "Run workflow" 按鈕

## 注意事項

### 環境要求
工作流程假設運行環境具有以下軟件：
- TI Code Composer Studio (CCS) 安裝在 `C:\ti\ccs2020\ccs\`
- Cppcheck 安裝在 `C:\Program Files\Cppcheck`

### 路徑配置
如果您的開發環境中 TI CCS 或 Cppcheck 安裝在不同位置，您可能需要：
1. 修改 dailyBuildConfigs 目錄中的批次檔路徑
2. 或者修改工作流程文件中的環境檢查步驟

### 錯誤處理
- 工作流程使用 `continue-on-error: true` 確保即使某個步驟失敗，其他步驟仍會執行
- 所有執行結果都會上傳為 artifacts，方便後續分析
- 提供詳細的執行摘要和狀態報告
- 使用 DOS 批次檔的 `call` 命令確保正確執行子批次檔

## 推薦使用

建議根據不同需求選擇合適的工作流程：

- **日常開發**: 使用 `execute-all-scripts.yml`（最簡潔，執行速度快）
- **完整構建**: 使用 `daily-build.yml`（包含定時執行和詳細環境檢查）
- **調試問題**: 使用 `execute-daily-build-scripts.yml`（分步執行，便於定位問題）

## 自定義

### 添加新的批次檔
1. 將新的 .bat 文件放入 dailyBuildConfigs 目錄
2. 如果使用 `execute-all-scripts.yml`，需要修改動態生成的腳本邏輯
3. 如果使用其他工作流程，需要添加相應的執行步驟

### 修改觸發條件
編輯工作流程文件中的 `on:` 部分來修改觸發條件。

### 修改執行順序
- `execute-all-scripts.yml`: 修改動態生成腳本的順序
- 其他工作流程: 調整 YAML 文件中步驟的順序

## 故障排除

### 查看執行日誌
1. 進入 Actions 頁面
2. 點擊具體的工作流程執行
3. 查看各個步驟的詳細日誌

### 下載 Artifacts
執行完成後，可以下載以下 artifacts：
- `cppcheck-results`: Cppcheck 分析結果
- `build-artifacts`: 構建產物
- `execution-logs`: 執行日誌

### 常見問題
1. **TI CCS 路徑錯誤**: 檢查並更新批次檔中的 CCS 安裝路徑
2. **Cppcheck 未找到**: 確保 Cppcheck 已正確安裝
3. **權限問題**: 確保 Gitea Runner 有足夠權限執行批次檔
