# Gitea Actions 工作流程說明

本目錄包含用於自動執行 dailyBuildConfigs 目錄中批次檔的 Gitea Actions 工作流程。

## 工作流程文件

### 1. `daily-build.yml`
完整的每日構建和代碼分析工作流程，包含：
- IDE 清理構建
- IDE 構建
- Cppcheck 代碼分析
- 構建產物上傳
- 詳細的執行報告

**觸發條件：**
- 推送到 main/master/develop 分支
- Pull Request 到 main/master/develop 分支
- 每日定時執行（UTC 時間 2:00 AM）
- 手動觸發

### 2. `execute-daily-build-scripts.yml`
簡化版工作流程，專門用於執行 dailyBuildConfigs 目錄中的所有批次檔：
- 自動發現並執行所有 .bat 文件
- 提供詳細的執行狀態報告
- 上傳執行日誌和結果文件

**觸發條件：**
- 推送到 main/master/develop 分支
- Pull Request 到 main/master/develop 分支
- 手動觸發

## 當前 dailyBuildConfigs 目錄中的批次檔

1. **cppchk.bat** - 執行 Cppcheck 代碼靜態分析
2. **ideCleanBuild.bat** - 執行 TI CCS IDE 清理構建
3. **idebuild.bat** - 執行 TI CCS IDE 構建

## 使用方法

### 自動觸發
工作流程會在以下情況自動執行：
- 當您推送代碼到主要分支時
- 當您創建或更新 Pull Request 時
- 每日定時執行（僅 daily-build.yml）

### 手動觸發
1. 進入 Gitea 倉庫頁面
2. 點擊 "Actions" 標籤
3. 選擇要執行的工作流程
4. 點擊 "Run workflow" 按鈕

## 注意事項

### 環境要求
工作流程假設運行環境具有以下軟件：
- TI Code Composer Studio (CCS) 安裝在 `C:\ti\ccs2020\ccs\`
- Cppcheck 安裝在 `C:\Program Files\Cppcheck`

### 路徑配置
如果您的開發環境中 TI CCS 或 Cppcheck 安裝在不同位置，您可能需要：
1. 修改 dailyBuildConfigs 目錄中的批次檔路徑
2. 或者修改工作流程文件中的環境檢查步驟

### 錯誤處理
- 工作流程使用 `continue-on-error: true` 確保即使某個步驟失敗，其他步驟仍會執行
- 所有執行結果都會上傳為 artifacts，方便後續分析
- 提供詳細的執行摘要和狀態報告

## 自定義

### 添加新的批次檔
只需將新的 .bat 文件放入 dailyBuildConfigs 目錄，工作流程會自動發現並執行它們。

### 修改觸發條件
編輯工作流程文件中的 `on:` 部分來修改觸發條件。

### 修改執行順序
如果需要特定的執行順序，可以修改 `execute-daily-build-scripts.yml` 中的腳本執行邏輯。

## 故障排除

### 查看執行日誌
1. 進入 Actions 頁面
2. 點擊具體的工作流程執行
3. 查看各個步驟的詳細日誌

### 下載 Artifacts
執行完成後，可以下載以下 artifacts：
- `cppcheck-results`: Cppcheck 分析結果
- `build-artifacts`: 構建產物
- `execution-logs`: 執行日誌

### 常見問題
1. **TI CCS 路徑錯誤**: 檢查並更新批次檔中的 CCS 安裝路徑
2. **Cppcheck 未找到**: 確保 Cppcheck 已正確安裝
3. **權限問題**: 確保 Gitea Runner 有足夠權限執行批次檔
