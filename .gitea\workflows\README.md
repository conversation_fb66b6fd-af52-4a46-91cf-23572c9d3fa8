# Gitea Actions 工作流程說明

本目錄包含用於自動執行 dailyBuildConfigs 目錄中批次檔的 Gitea Actions 工作流程。

## 工作流程文件

### `execute-all-scripts.yml`
最簡潔的版本，動態創建執行腳本：
- 動態生成一個批次檔來執行所有腳本
- 使用純 DOS 批次檔語法
- 提供錯誤計數和摘要報告

**觸發條件：**
- 推送到 main/master/develop 分支
- Pull Request 到 main/master/develop 分支
- 手動觸發

## 當前 dailyBuildConfigs 目錄中的批次檔

1. **ideCleanBuild.bat** - 執行 TI CCS IDE 清理構建
2. **idebuild.bat** - 執行 TI CCS IDE 構建
3. **cppchk.bat** - 執行 Cppcheck 代碼靜態分析（需要構建後的項目資料）

## 主要特點

1. **DOS Command Line**：工作流程使用 DOS 批次檔語法執行，確保與 Windows 環境完全兼容
2. **動態腳本生成**：自動創建執行腳本來運行所有批次檔
3. **錯誤處理**：即使某個批次檔失敗，其他的仍會繼續執行
4. **詳細報告**：提供每個批次檔的執行狀態和退出代碼
5. **產物保存**：自動上傳構建結果和分析報告
6. **簡潔高效**：最精簡的配置，執行速度快

## 使用方法

### 自動觸發
工作流程會在以下情況自動執行：
- 當您推送代碼到主要分支時
- 當您創建或更新 Pull Request 時

### 手動觸發
1. 進入 Gitea 倉庫頁面
2. 點擊 "Actions" 標籤
3. 選擇 "Execute All Daily Build Scripts" 工作流程
4. 點擊 "Run workflow" 按鈕

## 注意事項

### 環境要求
工作流程假設運行環境具有以下軟件：
- TI Code Composer Studio (CCS) 安裝在 `C:\ti\ccs2020\ccs\`
- Cppcheck 安裝在 `C:\Program Files\Cppcheck`

### 路徑配置
如果您的開發環境中 TI CCS 或 Cppcheck 安裝在不同位置，您需要：
1. 修改 dailyBuildConfigs 目錄中的批次檔路徑
2. 確保批次檔中的路徑與實際安裝位置一致

### 錯誤處理
- 工作流程使用 `continue-on-error: true` 確保即使某個步驟失敗，其他步驟仍會執行
- 所有執行結果都會上傳為 artifacts，方便後續分析
- 提供詳細的執行摘要和狀態報告
- 使用 DOS 批次檔的 `call` 命令確保正確執行子批次檔

## 工作流程執行

`execute-all-scripts.yml` 會自動執行以下步驟：

1. **檢出代碼**：從 Git 倉庫獲取最新代碼
2. **動態生成執行腳本**：創建一個批次檔來執行所有 dailyBuildConfigs 中的腳本
3. **執行批次檔**：依序運行 ideCleanBuild.bat → idebuild.bat → cppchk.bat
4. **上傳結果**：保存執行日誌和構建產物

## 自定義

### 添加新的批次檔
1. 將新的 .bat 文件放入 dailyBuildConfigs 目錄
2. 修改 `execute-all-scripts.yml` 中動態生成腳本的邏輯
3. 在生成腳本的部分添加新批次檔的執行命令

### 修改觸發條件
編輯 `execute-all-scripts.yml` 文件中的 `on:` 部分來修改觸發條件。

### 修改執行順序
修改 `execute-all-scripts.yml` 中動態生成腳本的順序，調整批次檔執行的先後順序。

## 故障排除

### 查看執行日誌
1. 進入 Actions 頁面
2. 點擊具體的工作流程執行
3. 查看各個步驟的詳細日誌

### 下載 Artifacts
執行完成後，可以下載以下 artifacts：
- `execution-logs`: 執行日誌、Cppcheck 分析結果、構建產物等

### 常見問題
1. **TI CCS 路徑錯誤**: 檢查並更新批次檔中的 CCS 安裝路徑
2. **Cppcheck 未找到**: 確保 Cppcheck 已正確安裝
3. **權限問題**: 確保 Gitea Runner 有足夠權限執行批次檔
