@echo off
echo Adding compile_commands.json to Git...
echo.

echo Checking if file exists...
if exist "Debug\.clangd\compile_commands.json" (
    echo [OK] compile_commands.json found
    echo File location: Debug\.clangd\compile_commands.json
    echo.
    
    echo Current Git status:
    git status --porcelain Debug\.clangd\compile_commands.json
    echo.
    
    echo Checking if file is tracked by Git:
    git ls-files Debug\.clangd\compile_commands.json
    if errorlevel 1 (
        echo [INFO] File is not currently tracked by Git
    ) else (
        echo [INFO] File is already tracked by Git
    )
    echo.
    
    echo Force adding file to Git (ignoring .gitignore):
    git add -f Debug\.clangd\compile_commands.json
    if errorlevel 1 (
        echo [ERROR] Failed to add file to Git
        exit /b 1
    ) else (
        echo [SUCCESS] File added to Git staging area
    )
    echo.
    
    echo Current Git status after adding:
    git status --porcelain Debug\.clangd\compile_commands.json
    echo.
    
    echo To commit this file, run:
    echo git commit -m "Add compile_commands.json for code analysis tools"
    echo.
    
) else (
    echo [ERROR] compile_commands.json not found at Debug\.clangd\compile_commands.json
    echo.
    echo Please make sure to run idebuild.bat first to generate the file
    echo.
    exit /b 1
)

echo.
echo ========================================
echo Summary:
echo ========================================
echo 1. File location: Debug\.clangd\compile_commands.json
echo 2. .gitignore rule: !Debug/.clangd/compile_commands.json
echo 3. File has been force-added to Git staging area
echo 4. Use 'git commit' to save the changes
echo.
echo Note: This file will now be tracked by Git and included
echo in future commits, allowing cppcheck to use it for analysis.
echo.
