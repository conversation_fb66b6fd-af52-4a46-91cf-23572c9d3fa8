@echo off
set CPPCHECK_PATH=C:\Program Files\Cppcheck
set TI_CLANG_PATH=C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin
set PATH=%PATH%;%TI_CLANG_PATH%;%CPPCHECK_PATH%
REM set PROJECT_PATH=.\Debug
REM cd %PROJECT_PATH%

REM Run cppcheck with TI ARM clang integration (without clang-tidy rules)
REM     --clang=tiarmclang ^cppchk
cppcheck --project=compile_commands.json ^
    --enable=all ^
    --enable=warning,style,performance,portability,information ^
    --std=c11 ^
    --suppress=missingInclude ^
    --suppress=missingIncludeSystem ^
    --template="{severity}: {message} [{id}] {file}({line})" ^
    --output-file=cppcheck-result.txt

REM Check for error-level issues (including security errors)
findstr /I /C:"error:" cppcheck-result.txt > nul
if %errorlevel% equ 0 (
    echo [ERROR] Critical or security issues found!
    echo.
    REM Display only error-level issues
    findstr /I /C:"error:" cppcheck-result.txt
    echo.
    echo For complete analysis report, please check cppcheck-result.txt
    exit /b 1
) else (
    echo [PASS] Check completed. No critical errors found.
    echo.
    echo Warnings and other suggestions:
    type cppcheck-result.txt
    exit /b 0
)
