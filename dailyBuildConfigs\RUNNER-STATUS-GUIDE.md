# Gitea act_runner 狀態檢查指南

本指南幫助您檢查 Gitea Actions 的 act_runner 是否正常運行。

## 🚀 快速檢查方法

### 1. 使用提供的檢查腳本

#### DOS 批次檔版本（推薦）
```cmd
check-act-runner.bat
```

#### PowerShell 版本（詳細信息）
```powershell
PowerShell -ExecutionPolicy Bypass -File check-act-runner.ps1
```

### 2. 手動檢查步驟

#### A. 檢查進程是否運行
```cmd
tasklist | findstr act_runner
```
或
```cmd
tasklist | findstr gitea
```

#### B. 檢查 Windows 服務
```cmd
sc query | findstr -i act
```

#### C. 檢查網路連接
```cmd
netstat -an | findstr :3000
netstat -an | findstr :8080
```

## 🌐 Gitea Web 界面檢查

### 1. 管理員檢查（需要管理員權限）
1. 登入 Gitea：`http://your-gitea-server:3000`
2. 進入 **Site Administration**
3. 點擊 **Actions** → **Runners**
4. 查看 Runner 列表和狀態

### 2. 倉庫級別檢查
1. 進入您的倉庫
2. 點擊 **Actions** 標籤
3. 查看工作流程執行歷史
4. 檢查是否有失敗的執行

## 🧪 測試 Runner 功能

### 使用測試工作流程
1. 提交 `test-runner.yml` 到您的倉庫
2. 進入 Actions 頁面
3. 選擇 "Test Runner Status" 工作流程
4. 點擊 **Run workflow** 手動觸發
5. 觀察執行結果

### 測試工作流程會檢查：
- ✅ 基本命令執行
- ✅ 環境變數
- ✅ 檔案操作
- ✅ 網路連接
- ✅ 開發工具可用性
- ✅ 倉庫內容存取

## 🔍 常見狀態指標

### ✅ Runner 正常運行的標誌
- 進程列表中有 `act_runner.exe` 或 `gitea-act-runner.exe`
- Gitea 管理界面顯示 Runner 為 "Online"
- 工作流程能夠成功執行
- 測試工作流程全部步驟通過

### ❌ Runner 問題的標誌
- 找不到 act_runner 進程
- Gitea 管理界面顯示 Runner 為 "Offline"
- 工作流程卡在 "Queued" 狀態
- 工作流程執行失敗並顯示 "No available runners"

## 🛠️ 故障排除

### 問題 1: Runner 進程未運行
**解決方案：**
```cmd
# 導航到 act_runner 安裝目錄
cd C:\path\to\act_runner

# 啟動 daemon
act_runner.exe daemon --config config.yaml
```

### 問題 2: Runner 未註冊
**解決方案：**
```cmd
# 重新註冊 runner
act_runner.exe register --instance http://your-gitea-url --token YOUR_REGISTRATION_TOKEN
```

### 問題 3: 配置問題
**檢查配置檔：**
```yaml
# config.yaml 範例
log:
  level: info
runner:
  file: .runner
  capacity: 1
  timeout: 3h
  insecure: false
  fetch_timeout: 5s
  fetch_interval: 2s
cache:
  enabled: true
  dir: ""
  host: ""
  port: 0
container:
  network: ""
  privileged: false
  options: ""
  workdir_parent: ""
host:
  workdir_parent: ""
```

### 問題 4: 權限問題
**解決方案：**
- 以管理員身份運行 act_runner
- 檢查檔案和目錄權限
- 確保 Windows 防火牆允許連接

### 問題 5: 網路連接問題
**檢查項目：**
- Gitea 服務器是否可達
- 防火牆設定
- 代理設定
- DNS 解析

## 📊 監控建議

### 1. 定期檢查
- 每日檢查 Runner 狀態
- 監控工作流程執行成功率
- 查看 Runner 日誌檔

### 2. 自動化監控
可以設定定時任務來執行檢查腳本：
```cmd
# 創建 Windows 排程任務
schtasks /create /tn "Check Act Runner" /tr "C:\path\to\check-act-runner.bat" /sc daily /st 09:00
```

### 3. 日誌檔位置
通常在以下位置查找日誌：
- `C:\act_runner\logs\`
- `%USERPROFILE%\act_runner\logs\`
- Runner 安裝目錄下的 `.log` 檔案

## 📞 獲取幫助

如果問題持續存在：

1. **檢查 Gitea 文檔**：https://docs.gitea.io/en-us/actions/
2. **查看 act_runner 日誌**：詳細錯誤信息
3. **檢查 Gitea 服務器日誌**：服務器端問題
4. **社群支援**：Gitea 社群論壇和 GitHub Issues

## 🎯 最佳實踐

1. **定期更新**：保持 act_runner 和 Gitea 版本最新
2. **備份配置**：定期備份 runner 配置檔
3. **監控資源**：確保足夠的系統資源
4. **安全設定**：使用適當的權限和網路安全設定
5. **文檔記錄**：記錄配置變更和問題解決過程

---

使用這些方法，您應該能夠有效地檢查和監控 Gitea act_runner 的運行狀態。
