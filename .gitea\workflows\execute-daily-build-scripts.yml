name: Execute Daily Build Scripts

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  workflow_dispatch:
    # Allow manual trigger from Gitea UI

jobs:
  execute-scripts:
    runs-on: windows-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: List dailyBuildConfigs directory
      run: |
        echo "Contents of dailyBuildConfigs directory:"
        if (Test-Path "dailyBuildConfigs") {
          dir dailyBuildConfigs
        } else {
          echo "Error: dailyBuildConfigs directory not found"
          exit 1
        }
      shell: powershell
      
    - name: Execute all batch files in dailyBuildConfigs
      run: |
        echo "Executing all batch files in dailyBuildConfigs directory..."
        cd dailyBuildConfigs
        
        # Get all .bat files in the directory
        $batFiles = Get-ChildItem -Filter "*.bat" | Sort-Object Name
        
        if ($batFiles.Count -eq 0) {
          echo "No batch files found in dailyBuildConfigs directory"
          exit 1
        }
        
        echo "Found $($batFiles.Count) batch file(s):"
        foreach ($file in $batFiles) {
          echo "  - $($file.Name)"
        }
        echo ""
        
        # Execute each batch file
        $exitCodes = @()
        foreach ($batFile in $batFiles) {
          echo "========================================="
          echo "Executing: $($batFile.Name)"
          echo "========================================="
          
          try {
            # Execute the batch file and capture exit code
            & ".\$($batFile.Name)"
            $exitCode = $LASTEXITCODE
            $exitCodes += $exitCode
            
            if ($exitCode -eq 0) {
              echo "✓ $($batFile.Name) completed successfully (exit code: $exitCode)"
            } else {
              echo "✗ $($batFile.Name) completed with errors (exit code: $exitCode)"
            }
          }
          catch {
            echo "✗ Error executing $($batFile.Name): $($_.Exception.Message)"
            $exitCodes += 1
          }
          
          echo ""
        }
        
        # Summary
        echo "========================================="
        echo "EXECUTION SUMMARY"
        echo "========================================="
        $successCount = ($exitCodes | Where-Object { $_ -eq 0 }).Count
        $totalCount = $exitCodes.Count
        
        for ($i = 0; $i -lt $batFiles.Count; $i++) {
          $status = if ($exitCodes[$i] -eq 0) { "SUCCESS" } else { "FAILED" }
          echo "$($batFiles[$i].Name): $status (exit code: $($exitCodes[$i]))"
        }
        
        echo ""
        echo "Total: $totalCount scripts"
        echo "Successful: $successCount"
        echo "Failed: $($totalCount - $successCount)"
        
        # Set overall exit code
        if ($successCount -eq $totalCount) {
          echo "All scripts executed successfully!"
          exit 0
        } else {
          echo "Some scripts failed. Check the logs above for details."
          exit 1
        }
      shell: powershell
      
    - name: Upload execution logs
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: execution-logs
        path: |
          cppcheck-result.txt
          Debug/
          *.log
          *.txt
        retention-days: 7
