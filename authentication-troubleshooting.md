# Gitea Actions 認證問題故障排除指南

## 🚨 常見認證錯誤

### 1. "authentication required" 錯誤
當使用外部 GitHub Actions 時可能出現此錯誤。

### 2. "access denied" 錯誤
Runner 沒有足夠權限存取倉庫。

### 3. "repository not found" 錯誤
Runner 無法找到或存取指定的倉庫。

## 🔧 解決方案

### 方案 1: 使用自包含工作流程（推薦）
我們已經修改了 `execute-all-scripts.yml` 來避免外部依賴：

```yaml
# 不使用外部 checkout action
- name: Verify repository access
  run: |
    chcp 65001
    echo Repository should be automatically available in Gitea Actions
    dir
  shell: cmd

# 不使用外部 upload action
- name: Archive execution logs locally
  run: |
    chcp 65001
    # 本地歸檔邏輯
  shell: cmd
```

### 方案 2: 配置 Gitea Runner 認證

#### A. 檢查 Runner 註冊
```cmd
# 檢查 runner 狀態
act_runner.exe daemon --config config.yaml

# 重新註冊 runner（如果需要）
act_runner.exe register --instance http://your-gitea-url --token YOUR_TOKEN
```

#### B. 檢查 Runner 配置
確保 `config.yaml` 中的設置正確：
```yaml
log:
  level: info
runner:
  file: .runner
  capacity: 1
  timeout: 3h
  insecure: false  # 如果使用 HTTPS，設為 false
  fetch_timeout: 5s
  fetch_interval: 2s
```

#### C. 檢查網路連接
```cmd
# 測試到 Gitea 服務器的連接
ping your-gitea-server
telnet your-gitea-server 3000
```

### 方案 3: 配置 Gitea 服務器設置

#### A. 檢查 Actions 設置
在 Gitea 管理界面中：
1. 進入 Site Administration
2. 檢查 Actions 是否啟用
3. 確認 Runner 註冊狀態

#### B. 檢查倉庫權限
確保：
- Runner 有存取倉庫的權限
- 倉庫的 Actions 功能已啟用
- 工作流程文件語法正確

### 方案 4: 使用替代的 Checkout 方法

#### A. 使用 Git 命令
```yaml
- name: Manual checkout
  run: |
    git clone http://your-gitea-server/user/repo.git .
    git checkout develop
  shell: cmd
```

#### B. 使用本地 Gitea Action（如果可用）
```yaml
- name: Checkout repository
  uses: gitea.com/gitea/checkout@v1
```

## 🛠️ 調試步驟

### 1. 檢查 Runner 日誌
查看 act_runner 的日誌文件：
```cmd
# 通常在 runner 安裝目錄
type runner.log
```

### 2. 檢查 Gitea 服務器日誌
在 Gitea 服務器上檢查相關日誌。

### 3. 測試簡單工作流程
創建一個最簡單的測試工作流程：
```yaml
name: Test Authentication
on: workflow_dispatch
jobs:
  test:
    runs-on: self-hosted
    steps:
    - name: Test basic commands
      run: |
        echo Current directory: %CD%
        dir
        echo Test completed
      shell: cmd
```

### 4. 檢查環境變數
```yaml
- name: Check environment
  run: |
    echo GITEA_ACTIONS_RUNNER_TOKEN: %GITEA_ACTIONS_RUNNER_TOKEN%
    echo RUNNER_WORKSPACE: %RUNNER_WORKSPACE%
    echo RUNNER_TEMP: %RUNNER_TEMP%
  shell: cmd
```

## 📋 最佳實踐

### 1. 使用自包含工作流程
- 避免依賴外部 GitHub Actions
- 使用純 DOS 命令實現功能
- 本地處理文件和結果

### 2. 正確配置 Runner
- 使用正確的 Gitea 服務器 URL
- 確保網路連接穩定
- 定期更新 Runner 版本

### 3. 監控和維護
- 定期檢查 Runner 狀態
- 監控工作流程執行
- 保持日誌記錄

## 🚀 當前工作流程狀態

我們已經修改了 `execute-all-scripts.yml` 來：
- ✅ 移除外部 `actions/checkout@v4` 依賴
- ✅ 移除外部 `actions/upload-artifact@v4` 依賴
- ✅ 使用純 DOS 命令實現所有功能
- ✅ 本地歸檔執行結果
- ✅ 避免所有認證相關問題

現在的工作流程應該能在 Gitea Actions 中正常運行，不會遇到認證問題。
