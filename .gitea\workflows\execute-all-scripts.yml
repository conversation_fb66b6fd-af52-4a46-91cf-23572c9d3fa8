name: Execute All Daily Build Scripts

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  workflow_dispatch:
    # Allow manual trigger from Gitea UI

jobs:
  execute-scripts:
    runs-on: host
    
    steps:
    - name: Checkout repository
      uses: http://${{ secrets.GIT_ACCESS_TOKEN }}:@************:3000/actions/checkout@v4
      
    - name: List dailyBuildConfigs directory
      run: |
        echo Contents of dailyBuildConfigs directory:
        if exist "dailyBuildConfigs" (
          dir dailyBuildConfigs
        ) else (
          echo Error: dailyBuildConfigs directory not found
          exit /b 1
        )
      shell: cmd
      
    - name: Execute ideCleanBuild.bat
      run: |
        echo Executing ideCleanBuild.bat...
        cd dailyBuildConfigs
        if exist "ideCleanBuild.bat" (
          echo =========================================
          echo Executing: ideCleanBuild.bat
          echo =========================================
          call ideCleanBuild.bat
          if errorlevel 1 (
            echo [FAILED] ideCleanBuild.bat completed with errors
          ) else (
            echo [SUCCESS] ideCleanBuild.bat completed successfully
          )
        ) else (
          echo Error: ideCleanBuild.bat not found
        )
        echo.
      shell: cmd
      continue-on-error: true
      
    - name: Execute idebuild.bat
      run: |
        echo Executing idebuild.bat...
        cd dailyBuildConfigs
        if exist "idebuild.bat" (
          echo =========================================
          echo Executing: idebuild.bat
          echo =========================================
          call idebuild.bat
          if errorlevel 1 (
            echo [FAILED] idebuild.bat completed with errors
          ) else (
            echo [SUCCESS] idebuild.bat completed successfully
          )
        ) else (
          echo Error: idebuild.bat not found
        )
        echo.
      shell: cmd
      continue-on-error: true
      
    - name: Execute cppchk.bat
      run: |
        chcp 65001 >nul
        echo Executing cppchk.bat...
        cd dailyBuildConfigs
        if exist "cppchk.bat" (
          echo =========================================
          echo Executing: cppchk.bat
          echo =========================================
          echo.
          echo Before execution - checking prerequisites:
          if exist "..\Debug\.clangd\compile_commands.json" (
            echo [OK] compile_commands.json found
          ) else (
            echo [WARNING] compile_commands.json not found - cppcheck may not work properly
          )
          echo.

          call cppchk.bat
          set cppchk_exit_code=%errorlevel%

          echo.
          echo Cppchk execution completed with exit code: %cppchk_exit_code%

          if exist "..\cppcheck-result.txt" (
            echo [INFO] Cppcheck result file created
            echo.
            echo First few lines of cppcheck results:
            type ..\cppcheck-result.txt | more
          ) else (
            echo [WARNING] Cppcheck result file not created
          )

          if %cppchk_exit_code% equ 0 (
            echo [SUCCESS] cppchk.bat completed successfully
          ) else (
            echo [FAILED] cppchk.bat completed with errors (exit code: %cppchk_exit_code%)
          )
        ) else (
          echo Error: cppchk.bat not found
        )
        echo.
      shell: cmd
      continue-on-error: true
      
    - name: Upload execution logs
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: execution-logs
        path: |
          cppcheck-result.txt
          Debug/
          *.log
          *.txt
          execute_all.bat
        retention-days: 7
