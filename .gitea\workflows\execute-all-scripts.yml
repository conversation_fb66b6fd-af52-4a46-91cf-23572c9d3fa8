name: Execute All Daily Build Scripts

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  workflow_dispatch:
    # Allow manual trigger from Gitea UI

jobs:
  execute-scripts:
    runs-on: self-hosted

    steps:
    - name: Setup workspace and checkout
      run: |
        chcp 65001
        echo Setting up workspace and checking out repository...
        echo Current directory: %CD%
        echo.

        REM Check if repository files are already available
        if exist "dailyBuildConfigs" (
          echo Repository files already available
          echo dailyBuildConfigs directory found
        ) else (
          echo Repository files not found, attempting manual checkout...

          REM Try to use git if available
          git --version >nul 2>&1
          if %errorlevel% equ 0 (
            echo Git is available, cloning repository...

            REM Use environment variables if available
            if defined GITEA_REPOSITORY_URL (
              echo Using GITEA_REPOSITORY_URL: %GITEA_REPOSITORY_URL%
              git clone %GITEA_REPOSITORY_URL% temp_repo
            ) else (
              echo Using hardcoded repository URL...
              git clone http://${{HrcQq9zIgdtaBUaVvNNv3vmUzejFK8HapfMAzj3x}}:@10.102.30.36:3000/hori_liu/bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang@develop temp_repo
            )

            if exist "temp_repo" (
              echo Copying files from temp repository...
              xcopy temp_repo\*.* . /E /I /Y
              rmdir /S /Q temp_repo
            )
          ) else (
            echo Git not available, cannot checkout repository
            echo Please ensure repository files are available in the workspace
            exit /b 1
          )
        )

        echo.
        echo Final workspace contents:
        dir
        echo.

        if exist "dailyBuildConfigs" (
          echo [SUCCESS] Repository checkout completed successfully
        ) else (
          echo [ERROR] Repository checkout failed - dailyBuildConfigs not found
          exit /b 1
        )
      shell: cmd

    - name: List dailyBuildConfigs directory
      run: |
        chcp 65001
        echo Contents of dailyBuildConfigs directory:
        if exist "dailyBuildConfigs" (
          dir dailyBuildConfigs
        ) else (
          echo Error: dailyBuildConfigs directory not found
          exit /b 1
        )
      shell: cmd
      
    - name: Execute ideCleanBuild.bat
      run: |
        chcp 65001
        echo Executing ideCleanBuild.bat...
        cd dailyBuildConfigs
        if exist "ideCleanBuild.bat" (
          echo =========================================
          echo Executing: ideCleanBuild.bat
          echo =========================================
          call ideCleanBuild.bat
          set exit_code=%errorlevel%
          if %exit_code% equ 0 (
            echo [SUCCESS] ideCleanBuild.bat completed successfully (exit code: %exit_code%)
          ) else (
            echo [FAILED] ideCleanBuild.bat completed with errors (exit code: %exit_code%)
          )
        ) else (
          echo Error: ideCleanBuild.bat not found
        )
        echo.
      shell: cmd
      continue-on-error: true

    - name: Execute idebuild.bat
      run: |
        chcp 65001
        echo Executing idebuild.bat...
        cd dailyBuildConfigs
        if exist "idebuild.bat" (
          echo =========================================
          echo Executing: idebuild.bat
          echo =========================================
          call idebuild.bat
          set exit_code=%errorlevel%
          if %exit_code% equ 0 (
            echo [SUCCESS] idebuild.bat completed successfully (exit code: %exit_code%)
            echo.
            echo Checking if compile_commands.json was generated:
            if exist "..\Debug\.clangd\compile_commands.json" (
              echo [OK] compile_commands.json found - ready for cppcheck
            ) else (
              echo [WARNING] compile_commands.json not found
            )
          ) else (
            echo [FAILED] idebuild.bat completed with errors (exit code: %exit_code%)
          )
        ) else (
          echo Error: idebuild.bat not found
        )
        echo.
      shell: cmd
      continue-on-error: true

    - name: Execute cppchk.bat
      run: |
        chcp 65001
        echo Executing cppchk.bat...
        cd dailyBuildConfigs
        if exist "cppchk.bat" (
          echo =========================================
          echo Executing: cppchk.bat
          echo =========================================
          echo.
          echo Before execution - checking prerequisites:
          if exist "..\Debug\.clangd\compile_commands.json" (
            echo [OK] compile_commands.json found
          ) else (
            echo [WARNING] compile_commands.json not found - cppcheck may not work properly
          )
          echo.

          call cppchk.bat
          set cppchk_exit_code=%errorlevel%

          echo.
          echo Cppchk execution completed with exit code: %cppchk_exit_code%

          REM Check if cppcheck result file was created
          if exist "..\cppcheck-result.txt" (
            echo [INFO] Cppcheck result file created
            echo.
            echo First 10 lines of cppcheck results:
            more +1 ..\cppcheck-result.txt | head -10
            echo.

            REM Check for actual errors in the result file
            findstr /I /C:"error:" ..\cppcheck-result.txt > nul
            if %errorlevel% equ 0 (
              echo [FAILED] Cppcheck found error-level issues
              set cppchk_exit_code=1
            ) else (
              echo [INFO] No error-level issues found by cppcheck
            )
          ) else (
            echo [WARNING] Cppcheck result file not created
            set cppchk_exit_code=1
          )

          if %cppchk_exit_code% equ 0 (
            echo [SUCCESS] cppchk.bat completed successfully
          ) else (
            echo [FAILED] cppchk.bat completed with errors (exit code: %cppchk_exit_code%)
          )
        ) else (
          echo Error: cppchk.bat not found
        )
        echo.
      shell: cmd
      continue-on-error: true

    - name: Archive execution logs locally
      run: |
        chcp 65001
        echo Creating local archive of execution logs...
        echo.

        REM Create archive directory with timestamp
        set timestamp=%DATE:~10,4%%DATE:~4,2%%DATE:~7,2%_%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%
        set timestamp=%timestamp: =0%
        set archive_dir=execution-logs-%timestamp%

        echo Creating archive directory: %archive_dir%
        if not exist "%archive_dir%" mkdir "%archive_dir%"

        REM Copy log files if they exist
        echo Copying result files...
        if exist "cppcheck-result.txt" (
          echo - Copying cppcheck-result.txt
          copy cppcheck-result.txt "%archive_dir%\"
        )

        REM Copy Debug directory if exists (but only .clangd/compile_commands.json)
        if exist "Debug\.clangd\compile_commands.json" (
          echo - Copying compile_commands.json
          if not exist "%archive_dir%\Debug\.clangd" mkdir "%archive_dir%\Debug\.clangd"
          copy "Debug\.clangd\compile_commands.json" "%archive_dir%\Debug\.clangd\"
        )

        REM Copy any log and text files
        echo - Copying log files
        for %%f in (*.log *.txt) do (
          if exist "%%f" (
            echo   Copying %%f
            copy "%%f" "%archive_dir%\"
          )
        )

        REM Show what was archived
        echo.
        echo Archive contents:
        if exist "%archive_dir%" (
          dir "%archive_dir%" /S
        ) else (
          echo No files archived
        )

        echo.
        echo ========================================
        echo Archive Summary
        echo ========================================
        echo Archive location: %CD%\%archive_dir%
        echo Files are available on the runner for inspection
        echo.
      shell: cmd
      if: always()
