name: Execute All Daily Build Scripts

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  workflow_dispatch:
    # Allow manual trigger from Gitea UI

jobs:
  execute-scripts:
    runs-on: windows-latest
    
    steps:
    - name: Verify repository content
      run: |
        chcp 65001
        echo Repository content verification...
        echo Current directory: %CD%
        echo.
        echo Directory contents:
        dir
        echo.
        echo Checking dailyBuildConfigs directory:
        if exist "dailyBuildConfigs" (
          echo dailyBuildConfigs directory found
          echo Contents of dailyBuildConfigs:
          dir dailyBuildConfigs
        ) else (
          echo ERROR: dailyBuildConfigs directory not found!
        )
        echo.
      shell: cmd
      
    - name: Create execution script
      run: |
        chcp 65001
        echo @echo off > execute_all.bat
        echo chcp 65001 >> execute_all.bat
        echo echo Starting execution of all daily build scripts... >> execute_all.bat
        echo echo. >> execute_all.bat
        echo echo Current working directory: %%CD%% >> execute_all.bat
        echo echo. >> execute_all.bat
        echo if not exist "dailyBuildConfigs" ^( >> execute_all.bat
        echo   echo ERROR: dailyBuildConfigs directory not found in %%CD%% >> execute_all.bat
        echo   echo Available directories: >> execute_all.bat
        echo   dir /AD >> execute_all.bat
        echo   exit /b 1 >> execute_all.bat
        echo ^) >> execute_all.bat
        echo cd dailyBuildConfigs >> execute_all.bat
        echo echo Changed to directory: %%CD%% >> execute_all.bat
        echo. >> execute_all.bat
        echo REM Check if directory exists >> execute_all.bat
        echo if not exist "." ^( >> execute_all.bat
        echo   echo Error: dailyBuildConfigs directory not found >> execute_all.bat
        echo   exit /b 1 >> execute_all.bat
        echo ^) >> execute_all.bat
        echo. >> execute_all.bat
        echo REM List available batch files >> execute_all.bat
        echo echo Available batch files: >> execute_all.bat
        echo dir *.bat /b 2^>nul >> execute_all.bat
        echo if errorlevel 1 ^( >> execute_all.bat
        echo   echo No batch files found >> execute_all.bat
        echo   exit /b 1 >> execute_all.bat
        echo ^) >> execute_all.bat
        echo echo. >> execute_all.bat
        echo. >> execute_all.bat
        echo REM Execute each batch file >> execute_all.bat
        echo set total_errors=0 >> execute_all.bat
        echo. >> execute_all.bat
        echo echo ========================================= >> execute_all.bat
        echo echo Executing: ideCleanBuild.bat >> execute_all.bat
        echo echo ========================================= >> execute_all.bat
        echo if exist "ideCleanBuild.bat" ^( >> execute_all.bat
        echo   call ideCleanBuild.bat >> execute_all.bat
        echo   if errorlevel 1 ^( >> execute_all.bat
        echo     echo [FAILED] ideCleanBuild.bat completed with errors >> execute_all.bat
        echo     set /a total_errors+=1 >> execute_all.bat
        echo   ^) else ^( >> execute_all.bat
        echo     echo [SUCCESS] ideCleanBuild.bat completed successfully >> execute_all.bat
        echo   ^) >> execute_all.bat
        echo ^) else ^( >> execute_all.bat
        echo   echo [SKIP] ideCleanBuild.bat not found >> execute_all.bat
        echo ^) >> execute_all.bat
        echo echo. >> execute_all.bat
        echo. >> execute_all.bat
        echo echo ========================================= >> execute_all.bat
        echo echo Executing: idebuild.bat >> execute_all.bat
        echo echo ========================================= >> execute_all.bat
        echo if exist "idebuild.bat" ^( >> execute_all.bat
        echo   call idebuild.bat >> execute_all.bat
        echo   if errorlevel 1 ^( >> execute_all.bat
        echo     echo [FAILED] idebuild.bat completed with errors >> execute_all.bat
        echo     set /a total_errors+=1 >> execute_all.bat
        echo   ^) else ^( >> execute_all.bat
        echo     echo [SUCCESS] idebuild.bat completed successfully >> execute_all.bat
        echo   ^) >> execute_all.bat
        echo ^) else ^( >> execute_all.bat
        echo   echo [SKIP] idebuild.bat not found >> execute_all.bat
        echo ^) >> execute_all.bat
        echo echo. >> execute_all.bat
        echo. >> execute_all.bat
        echo echo ========================================= >> execute_all.bat
        echo echo Executing: cppchk.bat >> execute_all.bat
        echo echo ========================================= >> execute_all.bat
        echo if exist "cppchk.bat" ^( >> execute_all.bat
        echo   call cppchk.bat >> execute_all.bat
        echo   if errorlevel 1 ^( >> execute_all.bat
        echo     echo [FAILED] cppchk.bat completed with errors >> execute_all.bat
        echo     set /a total_errors+=1 >> execute_all.bat
        echo   ^) else ^( >> execute_all.bat
        echo     echo [SUCCESS] cppchk.bat completed successfully >> execute_all.bat
        echo   ^) >> execute_all.bat
        echo ^) else ^( >> execute_all.bat
        echo   echo [SKIP] cppchk.bat not found >> execute_all.bat
        echo ^) >> execute_all.bat
        echo echo. >> execute_all.bat
        echo. >> execute_all.bat
        echo echo ========================================= >> execute_all.bat
        echo echo EXECUTION SUMMARY >> execute_all.bat
        echo echo ========================================= >> execute_all.bat
        echo if %%total_errors%% equ 0 ^( >> execute_all.bat
        echo   echo All scripts executed successfully! >> execute_all.bat
        echo   exit /b 0 >> execute_all.bat
        echo ^) else ^( >> execute_all.bat
        echo   echo %%total_errors%% script^(s^) failed. Check logs above. >> execute_all.bat
        echo   exit /b 1 >> execute_all.bat
        echo ^) >> execute_all.bat
        echo. >> execute_all.bat
        echo cd .. >> execute_all.bat
      shell: cmd
      
    - name: Execute all daily build scripts
      run: |
        chcp 65001
        echo Executing all daily build scripts...
        echo.
        echo Generated script content:
        type execute_all.bat
        echo.
        echo ========================================
        echo Starting script execution...
        echo ========================================
        call execute_all.bat
      shell: cmd
      
    - name: Archive execution logs
      run: |
        chcp 65001
        echo Archiving execution logs and results...
        echo.

        REM Create logs directory
        if not exist "logs" mkdir logs

        REM Copy log files if they exist
        if exist "cppcheck-result.txt" (
          echo Copying cppcheck-result.txt...
          copy cppcheck-result.txt logs\
        )

        if exist "execute_all.bat" (
          echo Copying execute_all.bat...
          copy execute_all.bat logs\
        )

        REM Copy any other log files
        for %%f in (*.log *.txt) do (
          if exist "%%f" (
            echo Copying %%f...
            copy "%%f" logs\
          )
        )

        REM Show what was archived
        echo.
        echo Archived files:
        if exist "logs" (
          dir logs
        ) else (
          echo No files to archive
        )

        echo.
        echo Note: Files are archived locally in the logs directory
        echo You can access them through the runner's file system
      shell: cmd
      if: always()
