name: Execute All Daily Build Scripts

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  workflow_dispatch:
    # Allow manual trigger from Gitea UI

jobs:
  execute-scripts:
    runs-on: self-hosted

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: List dailyBuildConfigs directory
      run: |
        echo Contents of dailyBuildConfigs directory:
        if exist "dailyBuildConfigs" (
          dir dailyBuildConfigs
        ) else (
          echo Error: dailyBuildConfigs directory not found
          exit /b 1
        )
      shell: cmd
      
    - name: Execute ideCleanBuild.bat
      run: |
        echo Executing ideCleanBuild.bat...
        cd dailyBuildConfigs
        if exist "ideCleanBuild.bat" (
          echo =========================================
          echo Executing: ideCleanBuild.bat
          echo =========================================
          call ideCleanBuild.bat
          if errorlevel 1 (
            echo [FAILED] ideCleanBuild.bat completed with errors
          ) else (
            echo [SUCCESS] ideCleanBuild.bat completed successfully
          )
        ) else (
          echo Error: ideCleanBuild.bat not found
        )
        echo.
      shell: cmd
      continue-on-error: true

    - name: Execute idebuild.bat
      run: |
        echo Executing idebuild.bat...
        cd dailyBuildConfigs
        if exist "idebuild.bat" (
          echo =========================================
          echo Executing: idebuild.bat
          echo =========================================
          call idebuild.bat
          if errorlevel 1 (
            echo [FAILED] idebuild.bat completed with errors
          ) else (
            echo [SUCCESS] idebuild.bat completed successfully
          )
        ) else (
          echo Error: idebuild.bat not found
        )
        echo.
      shell: cmd
      continue-on-error: true

    - name: Execute cppchk.bat
      run: |
        echo Executing cppchk.bat...
        cd dailyBuildConfigs
        if exist "cppchk.bat" (
          echo =========================================
          echo Executing: cppchk.bat
          echo =========================================
          call cppchk.bat
          if errorlevel 1 (
            echo [FAILED] cppchk.bat completed with errors
          ) else (
            echo [SUCCESS] cppchk.bat completed successfully
          )
        ) else (
          echo Error: cppchk.bat not found
        )
        echo.
      shell: cmd
      continue-on-error: true

    - name: Upload execution logs
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: execution-logs
        path: |
          cppcheck-result.txt
          Debug/
          *.log
          *.txt
          execute_all.bat
        retention-days: 7
