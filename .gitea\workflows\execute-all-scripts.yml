name: Execute All Daily Build Scripts

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  workflow_dispatch:
    # Allow manual trigger from Gitea UI

jobs:
  execute-scripts:
    runs-on: windows-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Create execution script
      run: |
        echo @echo off > execute_all.bat
        echo echo Starting execution of all daily build scripts... >> execute_all.bat
        echo echo. >> execute_all.bat
        echo cd dailyBuildConfigs >> execute_all.bat
        echo. >> execute_all.bat
        echo REM Check if directory exists >> execute_all.bat
        echo if not exist "." ^( >> execute_all.bat
        echo   echo Error: dailyBuildConfigs directory not found >> execute_all.bat
        echo   exit /b 1 >> execute_all.bat
        echo ^) >> execute_all.bat
        echo. >> execute_all.bat
        echo REM List available batch files >> execute_all.bat
        echo echo Available batch files: >> execute_all.bat
        echo dir *.bat /b 2^>nul >> execute_all.bat
        echo if errorlevel 1 ^( >> execute_all.bat
        echo   echo No batch files found >> execute_all.bat
        echo   exit /b 1 >> execute_all.bat
        echo ^) >> execute_all.bat
        echo echo. >> execute_all.bat
        echo. >> execute_all.bat
        echo REM Execute each batch file >> execute_all.bat
        echo set total_errors=0 >> execute_all.bat
        echo. >> execute_all.bat
        echo echo ========================================= >> execute_all.bat
        echo echo Executing: cppchk.bat >> execute_all.bat
        echo echo ========================================= >> execute_all.bat
        echo if exist "cppchk.bat" ^( >> execute_all.bat
        echo   call cppchk.bat >> execute_all.bat
        echo   if errorlevel 1 ^( >> execute_all.bat
        echo     echo [FAILED] cppchk.bat completed with errors >> execute_all.bat
        echo     set /a total_errors+=1 >> execute_all.bat
        echo   ^) else ^( >> execute_all.bat
        echo     echo [SUCCESS] cppchk.bat completed successfully >> execute_all.bat
        echo   ^) >> execute_all.bat
        echo ^) else ^( >> execute_all.bat
        echo   echo [SKIP] cppchk.bat not found >> execute_all.bat
        echo ^) >> execute_all.bat
        echo echo. >> execute_all.bat
        echo. >> execute_all.bat
        echo echo ========================================= >> execute_all.bat
        echo echo Executing: ideCleanBuild.bat >> execute_all.bat
        echo echo ========================================= >> execute_all.bat
        echo if exist "ideCleanBuild.bat" ^( >> execute_all.bat
        echo   call ideCleanBuild.bat >> execute_all.bat
        echo   if errorlevel 1 ^( >> execute_all.bat
        echo     echo [FAILED] ideCleanBuild.bat completed with errors >> execute_all.bat
        echo     set /a total_errors+=1 >> execute_all.bat
        echo   ^) else ^( >> execute_all.bat
        echo     echo [SUCCESS] ideCleanBuild.bat completed successfully >> execute_all.bat
        echo   ^) >> execute_all.bat
        echo ^) else ^( >> execute_all.bat
        echo   echo [SKIP] ideCleanBuild.bat not found >> execute_all.bat
        echo ^) >> execute_all.bat
        echo echo. >> execute_all.bat
        echo. >> execute_all.bat
        echo echo ========================================= >> execute_all.bat
        echo echo Executing: idebuild.bat >> execute_all.bat
        echo echo ========================================= >> execute_all.bat
        echo if exist "idebuild.bat" ^( >> execute_all.bat
        echo   call idebuild.bat >> execute_all.bat
        echo   if errorlevel 1 ^( >> execute_all.bat
        echo     echo [FAILED] idebuild.bat completed with errors >> execute_all.bat
        echo     set /a total_errors+=1 >> execute_all.bat
        echo   ^) else ^( >> execute_all.bat
        echo     echo [SUCCESS] idebuild.bat completed successfully >> execute_all.bat
        echo   ^) >> execute_all.bat
        echo ^) else ^( >> execute_all.bat
        echo   echo [SKIP] idebuild.bat not found >> execute_all.bat
        echo ^) >> execute_all.bat
        echo echo. >> execute_all.bat
        echo. >> execute_all.bat
        echo echo ========================================= >> execute_all.bat
        echo echo EXECUTION SUMMARY >> execute_all.bat
        echo echo ========================================= >> execute_all.bat
        echo if %%total_errors%% equ 0 ^( >> execute_all.bat
        echo   echo All scripts executed successfully! >> execute_all.bat
        echo   exit /b 0 >> execute_all.bat
        echo ^) else ^( >> execute_all.bat
        echo   echo %%total_errors%% script^(s^) failed. Check logs above. >> execute_all.bat
        echo   exit /b 1 >> execute_all.bat
        echo ^) >> execute_all.bat
        echo. >> execute_all.bat
        echo cd .. >> execute_all.bat
      shell: cmd
      
    - name: Execute all daily build scripts
      run: |
        echo Executing all daily build scripts...
        call execute_all.bat
      shell: cmd
      
    - name: Upload execution logs
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: execution-logs
        path: |
          cppcheck-result.txt
          Debug/
          *.log
          *.txt
          execute_all.bat
        retention-days: 7
