name: Execute All Daily Build Scripts

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  workflow_dispatch:
    # Allow manual trigger from Gitea UI

jobs:
  execute-scripts:
    runs-on: self-hosted

    steps:
    - name: Checkout repository
      uses: http://************:3000/hori_liu/bq769x2_TIDA010247_LP_MSPM0G3519_nortos_ticlang@develop

    - name: List dailyBuildConfigs directory
      run: |
        chcp 65001
        echo Contents of dailyBuildConfigs directory:
        if exist "dailyBuildConfigs" (
          dir dailyBuildConfigs
        ) else (
          echo Error: dailyBuildConfigs directory not found
          exit /b 1
        )
      shell: cmd
      
    - name: Execute ideCleanBuild.bat
      run: |
        chcp 65001
        echo Executing ideCleanBuild.bat...
        cd dailyBuildConfigs
        if exist "ideCleanBuild.bat" (
          echo =========================================
          echo Executing: ideCleanBuild.bat
          echo =========================================
          call ideCleanBuild.bat
          set exit_code=%errorlevel%
          if %exit_code% equ 0 (
            echo [SUCCESS] ideCleanBuild.bat completed successfully (exit code: %exit_code%)
          ) else (
            echo [FAILED] ideCleanBuild.bat completed with errors (exit code: %exit_code%)
          )
        ) else (
          echo Error: ideCleanBuild.bat not found
        )
        echo.
      shell: cmd
      continue-on-error: true

    - name: Execute idebuild.bat
      run: |
        chcp 65001
        echo Executing idebuild.bat...
        cd dailyBuildConfigs
        if exist "idebuild.bat" (
          echo =========================================
          echo Executing: idebuild.bat
          echo =========================================
          call idebuild.bat
          set exit_code=%errorlevel%
          if %exit_code% equ 0 (
            echo [SUCCESS] idebuild.bat completed successfully (exit code: %exit_code%)
            echo.
            echo Checking if compile_commands.json was generated:
            if exist "..\Debug\.clangd\compile_commands.json" (
              echo [OK] compile_commands.json found - ready for cppcheck
            ) else (
              echo [WARNING] compile_commands.json not found
            )
          ) else (
            echo [FAILED] idebuild.bat completed with errors (exit code: %exit_code%)
          )
        ) else (
          echo Error: idebuild.bat not found
        )
        echo.
      shell: cmd
      continue-on-error: true

    - name: Execute cppchk.bat
      run: |
        chcp 65001
        echo Executing cppchk.bat...
        cd dailyBuildConfigs
        if exist "cppchk.bat" (
          echo =========================================
          echo Executing: cppchk.bat
          echo =========================================
          echo.
          echo Before execution - checking prerequisites:
          if exist "..\Debug\.clangd\compile_commands.json" (
            echo [OK] compile_commands.json found
          ) else (
            echo [WARNING] compile_commands.json not found - cppcheck may not work properly
          )
          echo.

          call cppchk.bat
          set cppchk_exit_code=%errorlevel%

          echo.
          echo Cppchk execution completed with exit code: %cppchk_exit_code%

          REM Check if cppcheck result file was created
          if exist "..\cppcheck-result.txt" (
            echo [INFO] Cppcheck result file created
            echo.
            echo First 10 lines of cppcheck results:
            more +1 ..\cppcheck-result.txt | head -10
            echo.

            REM Check for actual errors in the result file
            findstr /I /C:"error:" ..\cppcheck-result.txt > nul
            if %errorlevel% equ 0 (
              echo [FAILED] Cppcheck found error-level issues
              set cppchk_exit_code=1
            ) else (
              echo [INFO] No error-level issues found by cppcheck
            )
          ) else (
            echo [WARNING] Cppcheck result file not created
            set cppchk_exit_code=1
          )

          if %cppchk_exit_code% equ 0 (
            echo [SUCCESS] cppchk.bat completed successfully
          ) else (
            echo [FAILED] cppchk.bat completed with errors (exit code: %cppchk_exit_code%)
          )
        ) else (
          echo Error: cppchk.bat not found
        )
        echo.
      shell: cmd
      continue-on-error: true

    - name: Upload execution logs
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: execution-logs
        path: |
          cppcheck-result.txt
          Debug/
          *.log
          *.txt
          execute_all.bat
        retention-days: 7
