name: Debug Daily Build Scripts

on:
  workflow_dispatch:
    # Manual trigger only for debugging

jobs:
  debug-scripts:
    runs-on: windows-latest
    
    steps:
    - name: Debug repository structure
      run: |
        chcp 65001
        echo ========================================
        echo Debug Repository Structure
        echo ========================================
        echo.
        echo Current directory: %CD%
        echo.
        echo Root directory contents:
        dir
        echo.
        echo Checking dailyBuildConfigs directory:
        if exist "dailyBuildConfigs" (
          echo [OK] dailyBuildConfigs directory found
          echo.
          echo Contents of dailyBuildConfigs:
          dir dailyBuildConfigs /B
          echo.
          echo Detailed listing:
          dir dailyBuildConfigs
          echo.
          echo Checking specific files:
          if exist "dailyBuildConfigs\ideCleanBuild.bat" (
            echo [OK] ideCleanBuild.bat found
          ) else (
            echo [ERROR] ideCleanBuild.bat NOT found
          )
          if exist "dailyBuildConfigs\idebuild.bat" (
            echo [OK] idebuild.bat found
          ) else (
            echo [ERROR] idebuild.bat NOT found
          )
          if exist "dailyBuildConfigs\cppchk.bat" (
            echo [OK] cppchk.bat found
          ) else (
            echo [ERROR] cppchk.bat NOT found
          )
        ) else (
          echo [ERROR] dailyBuildConfigs directory NOT found
          echo Available directories:
          dir /AD
        )
        echo.
      shell: cmd
      
    - name: Test simple script execution
      run: |
        chcp 65001
        echo ========================================
        echo Test Simple Script Execution
        echo ========================================
        echo.
        
        REM Create a simple test script
        echo @echo off > test_simple.bat
        echo chcp 65001 >> test_simple.bat
        echo echo This is a test script >> test_simple.bat
        echo echo Current directory: %%CD%% >> test_simple.bat
        echo if exist "dailyBuildConfigs" ^( >> test_simple.bat
        echo   echo dailyBuildConfigs directory exists >> test_simple.bat
        echo   cd dailyBuildConfigs >> test_simple.bat
        echo   echo Changed to: %%CD%% >> test_simple.bat
        echo   echo Files in this directory: >> test_simple.bat
        echo   dir *.bat /B >> test_simple.bat
        echo   cd .. >> test_simple.bat
        echo ^) else ^( >> test_simple.bat
        echo   echo dailyBuildConfigs directory does not exist >> test_simple.bat
        echo ^) >> test_simple.bat
        
        echo.
        echo Generated test script:
        type test_simple.bat
        echo.
        echo Executing test script:
        call test_simple.bat
        echo.
      shell: cmd
      
    - name: Test direct file access
      run: |
        chcp 65001
        echo ========================================
        echo Test Direct File Access
        echo ========================================
        echo.
        
        cd dailyBuildConfigs
        echo Current directory after cd: %CD%
        echo.
        echo Files in current directory:
        dir *.bat
        echo.
        echo Testing file existence:
        if exist "ideCleanBuild.bat" (
          echo [OK] ideCleanBuild.bat exists in current directory
          echo File size and date:
          dir ideCleanBuild.bat
        ) else (
          echo [ERROR] ideCleanBuild.bat does not exist in current directory
        )
        
        if exist "idebuild.bat" (
          echo [OK] idebuild.bat exists in current directory
          echo File size and date:
          dir idebuild.bat
        ) else (
          echo [ERROR] idebuild.bat does not exist in current directory
        )
        
        if exist "cppchk.bat" (
          echo [OK] cppchk.bat exists in current directory
          echo File size and date:
          dir cppchk.bat
        ) else (
          echo [ERROR] cppchk.bat does not exist in current directory
        )
        
        cd ..
        echo.
      shell: cmd
