name: Test Runner Status

on:
  workflow_dispatch:
    # Manual trigger only - use this to test if runner is working
  
jobs:
  test-runner:
    runs-on: windows-latest
    
    steps:
    - name: Test Basic Commands
      run: |
        echo ========================================
        echo Testing act_runner basic functionality
        echo ========================================
        echo.
        echo Current date and time: %DATE% %TIME%
        echo Computer name: %COMPUTERNAME%
        echo User: %USERNAME%
        echo Current directory: %CD%
        echo.
      shell: cmd
      
    - name: Test Environment Variables
      run: |
        echo ========================================
        echo Environment Information
        echo ========================================
        echo.
        echo GitHub Context:
        echo Repository: ${{ github.repository }}
        echo Ref: ${{ github.ref }}
        echo SHA: ${{ github.sha }}
        echo Actor: ${{ github.actor }}
        echo Event: ${{ github.event_name }}
        echo.
        echo System Information:
        systeminfo | findstr /C:"OS Name" /C:"OS Version" /C:"System Type"
        echo.
      shell: cmd
      
    - name: Test File Operations
      run: |
        echo ========================================
        echo Testing File Operations
        echo ========================================
        echo.
        echo Creating test file...
        echo This is a test file created by Gitea Actions > test-file.txt
        echo Test content line 2 >> test-file.txt
        echo.
        echo Reading test file:
        type test-file.txt
        echo.
        echo Deleting test file...
        del test-file.txt
        echo Test file operations completed successfully!
        echo.
      shell: cmd
      
    - name: Test Network Connectivity
      run: |
        echo ========================================
        echo Testing Network Connectivity
        echo ========================================
        echo.
        echo Testing DNS resolution...
        nslookup google.com
        echo.
        echo Testing ping...
        ping -n 2 *******
        echo.
      shell: cmd
      continue-on-error: true
      
    - name: Test PowerShell Availability
      run: |
        echo ========================================
        echo Testing PowerShell
        echo ========================================
        echo.
        echo PowerShell version:
        powershell -Command "Get-Host | Select-Object Version"
        echo.
        echo PowerShell execution policy:
        powershell -Command "Get-ExecutionPolicy"
        echo.
      shell: cmd
      continue-on-error: true
      
    - name: Check Available Tools
      run: |
        echo ========================================
        echo Checking Available Development Tools
        echo ========================================
        echo.
        echo Checking for Git...
        git --version 2>nul && echo Git is available || echo Git not found
        echo.
        echo Checking for Node.js...
        node --version 2>nul && echo Node.js is available || echo Node.js not found
        echo.
        echo Checking for Python...
        python --version 2>nul && echo Python is available || echo Python not found
        echo.
        echo Checking for Java...
        java -version 2>nul && echo Java is available || echo Java not found
        echo.
        echo Checking for .NET...
        dotnet --version 2>nul && echo .NET is available || echo .NET not found
        echo.
      shell: cmd
      continue-on-error: true
      
    - name: Test Checkout Action
      uses: actions/checkout@v4
      
    - name: Verify Repository Content
      run: |
        echo ========================================
        echo Repository Content Verification
        echo ========================================
        echo.
        echo Current directory contents:
        dir
        echo.
        echo Checking for dailyBuildConfigs:
        if exist "dailyBuildConfigs" (
          echo [OK] dailyBuildConfigs directory found
          echo Contents:
          dir dailyBuildConfigs
        ) else (
          echo [WARNING] dailyBuildConfigs directory not found
        )
        echo.
        echo Checking for .gitea workflows:
        if exist ".gitea\workflows" (
          echo [OK] .gitea\workflows directory found
          echo Workflow files:
          dir .gitea\workflows\*.yml
        ) else (
          echo [WARNING] .gitea\workflows directory not found
        )
        echo.
      shell: cmd
      
    - name: Final Status Report
      run: |
        echo ========================================
        echo RUNNER TEST COMPLETED SUCCESSFULLY!
        echo ========================================
        echo.
        echo If you can see this message, your act_runner is:
        echo [✓] Properly connected to Gitea
        echo [✓] Able to execute workflows
        echo [✓] Able to run DOS commands
        echo [✓] Able to access repository content
        echo [✓] Able to use GitHub Actions (checkout)
        echo.
        echo Your Gitea Actions setup is working correctly!
        echo.
        echo Next steps:
        echo 1. You can now safely use your daily build workflows
        echo 2. Monitor the Actions tab for workflow executions
        echo 3. Check runner logs if you encounter any issues
        echo.
      shell: cmd
